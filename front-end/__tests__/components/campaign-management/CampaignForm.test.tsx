import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CampaignForm from '@/components/campaign-management/CampaignForm';

const mockOnSubmit = jest.fn();
const mockOnCancel = jest.fn();

// Mock fetch for advertisers
global.fetch = jest.fn();

describe('CampaignForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve([
        { id: 'adv-1', name: 'Test Advertiser', email: '<EMAIL>' }
      ])
    });
  });

  it('renders create form correctly', async () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText('Create New Campaign')).toBeInTheDocument();
    expect(screen.getByLabelText('Campaign Title *')).toBeInTheDocument();
    expect(screen.getByLabelText('Advertiser *')).toBeInTheDocument();
    expect(screen.getByLabelText('Campaign Description *')).toBeInTheDocument();
    expect(screen.getByText('Create Campaign')).toBeInTheDocument();
  });

  it('renders edit form correctly', async () => {
    const mockCampaign = {
      id: 'camp-1',
      title: 'Test Campaign',
      description: 'Test Description',
      advertiserId: 'adv-1',
      advertiser: { id: 'adv-1', name: 'Test Advertiser', email: '<EMAIL>' },
      startDate: '2024-01-01T00:00:00.000Z',
      endDate: '2024-02-01T00:00:00.000Z',
      status: 'draft' as const,
      assignedPublisherIds: [],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    };

    render(
      <CampaignForm
        campaign={mockCampaign}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        isEditing={true}
      />
    );

    expect(screen.getByText('Edit Campaign')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test Campaign')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test Description')).toBeInTheDocument();
    expect(screen.getByText('Update Campaign')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const submitButton = screen.getByText('Create Campaign');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Campaign title is required')).toBeInTheDocument();
      expect(screen.getByText('Campaign description is required')).toBeInTheDocument();
      expect(screen.getByText('Please select an advertiser')).toBeInTheDocument();
      expect(screen.getByText('Start date is required')).toBeInTheDocument();
      expect(screen.getByText('End date is required')).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('validates date logic', async () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Fill required fields
    fireEvent.change(screen.getByLabelText('Campaign Title *'), {
      target: { value: 'Test Campaign' }
    });
    fireEvent.change(screen.getByLabelText('Campaign Description *'), {
      target: { value: 'Test Description' }
    });

    // Wait for advertisers to load
    await waitFor(() => {
      expect(screen.getByText('Test Advertiser (<EMAIL>)')).toBeInTheDocument();
    });

    fireEvent.change(screen.getByLabelText('Advertiser *'), {
      target: { value: 'adv-1' }
    });

    // Set end date before start date
    fireEvent.change(screen.getByLabelText('Start Date *'), {
      target: { value: '2024-02-01' }
    });
    fireEvent.change(screen.getByLabelText('End Date *'), {
      target: { value: '2024-01-01' }
    });

    const submitButton = screen.getByText('Create Campaign');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('End date must be after start date')).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('submits form with valid data', async () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Fill required fields
    fireEvent.change(screen.getByLabelText('Campaign Title *'), {
      target: { value: 'Test Campaign' }
    });
    fireEvent.change(screen.getByLabelText('Campaign Description *'), {
      target: { value: 'Test Description' }
    });

    // Wait for advertisers to load
    await waitFor(() => {
      expect(screen.getByText('Test Advertiser (<EMAIL>)')).toBeInTheDocument();
    });

    fireEvent.change(screen.getByLabelText('Advertiser *'), {
      target: { value: 'adv-1' }
    });
    fireEvent.change(screen.getByLabelText('Start Date *'), {
      target: { value: '2024-01-01' }
    });
    fireEvent.change(screen.getByLabelText('End Date *'), {
      target: { value: '2024-02-01' }
    });

    const submitButton = screen.getByText('Create Campaign');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Campaign',
          description: 'Test Description',
          advertiserId: 'adv-1',
          startDate: '2024-01-01',
          endDate: '2024-02-01'
        })
      );
    });
  });

  it('handles deliverables management', async () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const deliverableInput = screen.getByPlaceholderText('Add a deliverable');
    const addButton = screen.getByText('Add');

    // Add deliverable
    fireEvent.change(deliverableInput, { target: { value: 'Test Deliverable' } });
    fireEvent.click(addButton);

    expect(screen.getByText('Test Deliverable')).toBeInTheDocument();
    expect(deliverableInput).toHaveValue('');

    // Remove deliverable
    const removeButton = screen.getByRole('button', { name: /remove/i });
    fireEvent.click(removeButton);

    expect(screen.queryByText('Test Deliverable')).not.toBeInTheDocument();
  });

  it('handles cancel action', () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('shows loading state during submission', async () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        loading={true}
      />
    );

    expect(screen.getByText('Creating...')).toBeInTheDocument();
    expect(screen.getByText('Creating...')).toBeDisabled();
  });

  it('handles budget validation', async () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const budgetInput = screen.getByLabelText('Budget ($)');
    fireEvent.change(budgetInput, { target: { value: '-100' } });

    const submitButton = screen.getByText('Create Campaign');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Budget must be a positive number')).toBeInTheDocument();
    });
  });

  it('handles application deadline validation', async () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Set application deadline after start date
    fireEvent.change(screen.getByLabelText('Start Date *'), {
      target: { value: '2024-01-01' }
    });
    fireEvent.change(screen.getByLabelText('Application Deadline'), {
      target: { value: '2024-01-02' }
    });

    const submitButton = screen.getByText('Create Campaign');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Application deadline must be before start date')).toBeInTheDocument();
    });
  });

  it('handles fetch error for advertisers', async () => {
    (fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Should fall back to mock data
    await waitFor(() => {
      expect(screen.getByText('Tech Corp (<EMAIL>)')).toBeInTheDocument();
    });
  });

  it('clears errors when user starts typing', async () => {
    render(
      <CampaignForm
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const titleInput = screen.getByLabelText('Campaign Title *');
    const submitButton = screen.getByText('Create Campaign');

    // Trigger validation error
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Campaign title is required')).toBeInTheDocument();
    });

    // Start typing to clear error
    fireEvent.change(titleInput, { target: { value: 'Test' } });

    expect(screen.queryByText('Campaign title is required')).not.toBeInTheDocument();
  });
});
