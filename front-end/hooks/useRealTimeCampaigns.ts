'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectUser } from '@/store/authSlice';

interface Campaign {
  id: string;
  title: string;
  description: string;
  advertiserId: string;
  advertiser: {
    id: string;
    name: string;
    email: string;
  };
  startDate: string;
  endDate: string;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  assignedPublisherIds: string[];
  createdAt: string;
  updatedAt: string;
  budget?: number;
  requirements?: string;
  targetAudience?: string;
  contentGuidelines?: string;
  deliverables?: string[];
  applicationDeadline?: string;
  publisherAssignments?: {
    id: string;
    publisher: {
      id: string;
      name: string;
      email: string;
    };
    assignedAt: string;
    isActive: boolean;
  }[];
  applications?: {
    id: string;
    publisher: {
      id: string;
      name: string;
      email: string;
    };
    status: 'pending' | 'sp_approved' | 'approved' | 'rejected';
    appliedAt: string;
  }[];
  videoSubmissions?: {
    id: string;
    publisher: {
      id: string;
      name: string;
      email: string;
    };
    status: 'pending' | 'sp_review' | 'advertiser_review' | 'approved' | 'rejected' | 'published';
    submittedAt: string;
  }[];
}

interface UseRealTimeCampaignsReturn {
  campaigns: Campaign[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  lastUpdated: Date | null;
}

const useRealTimeCampaigns = (
  pollingInterval: number = 60000 // 60 seconds default for campaigns
): UseRealTimeCampaignsReturn => {
  const user = useSelector(selectUser);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  const fetchCampaigns = useCallback(async () => {
    if (!user?.id || user.role !== 'staff') {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      
      const response = await fetch(`http://localhost:3000/campaign`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Unauthorized. Please log in again.');
        }
        if (response.status === 403) {
          throw new Error('Access denied. SP Team role required.');
        }
        throw new Error(`Failed to fetch campaigns: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform data to include mock relations (in real app, backend should provide this)
      const campaignsWithMockData = Array.isArray(data) ? data.map((campaign: any, index: number) => ({
        id: campaign.id || `camp-${index}`,
        title: campaign.title || `Campaign ${index + 1}`,
        description: campaign.description || 'Campaign description',
        advertiserId: campaign.advertiserId || `adv-${index}`,
        advertiser: {
          id: campaign.advertiserId || `adv-${index}`,
          name: `Advertiser ${index + 1}`,
          email: `advertiser${index + 1}@example.com`
        },
        startDate: campaign.startDate || new Date().toISOString(),
        endDate: campaign.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        status: campaign.status || 'draft',
        assignedPublisherIds: campaign.assignedPublisherIds || [],
        createdAt: campaign.createdAt || new Date().toISOString(),
        updatedAt: campaign.updatedAt || new Date().toISOString(),
        budget: campaign.budget,
        requirements: campaign.requirements,
        targetAudience: campaign.targetAudience,
        contentGuidelines: campaign.contentGuidelines,
        deliverables: campaign.deliverables,
        applicationDeadline: campaign.applicationDeadline,
        publisherAssignments: [],
        applications: [],
        videoSubmissions: []
      })) : [];
      
      // Only update state if component is still mounted
      if (mountedRef.current) {
        setCampaigns(campaignsWithMockData);
        setLastUpdated(new Date());
      }
    } catch (err) {
      if (mountedRef.current) {
        setError(err instanceof Error ? err.message : 'Failed to fetch campaigns');
      }
    }
  }, [user]);

  // Manual refetch function
  const refetch = useCallback(async () => {
    setLoading(true);
    await fetchCampaigns();
    setLoading(false);
  }, [fetchCampaigns]);

  // Start polling
  const startPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
    }
    
    pollingRef.current = setInterval(() => {
      if (mountedRef.current) {
        fetchCampaigns();
      }
    }, pollingInterval);
  }, [fetchCampaigns, pollingInterval]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
  }, []);

  // Initial fetch and setup polling
  useEffect(() => {
    mountedRef.current = true;
    
    if (user && user.role === 'staff') {
      const fetchData = async () => {
        setLoading(true);
        await fetchCampaigns();
        setLoading(false);
      };
      
      fetchData();
      startPolling();
    }

    return () => {
      mountedRef.current = false;
      stopPolling();
    };
  }, [user, fetchCampaigns, startPolling, stopPolling]);

  // Handle visibility change to pause/resume polling
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling();
      } else if (user && user.role === 'staff') {
        startPolling();
        // Fetch immediately when tab becomes visible
        fetchCampaigns();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user, fetchCampaigns, startPolling, stopPolling]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    campaigns,
    loading,
    error,
    refetch,
    lastUpdated
  };
};

export default useRealTimeCampaigns;
